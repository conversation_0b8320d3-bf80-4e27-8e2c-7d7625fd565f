import { Wand2 } from "lucide-react";
import React from "react";

export function StudioHero() {
  return (
    <div className="flex justify-center">
      {/* Essential icon container only */}
      <div className="size-10 shrink-0 p-0 sm:size-14 lg:size-14">
        <div className="animate-gradient-move flex size-full items-center justify-center rounded-full border-2 border-white/20 bg-gradient-to-br from-violet-600 to-blue-500 p-2 sm:p-2 lg:p-2">
          <Wand2 className="size-full text-white drop-shadow-xl" />
        </div>
      </div>
      <div className="inline-block align-top">
        <h1 className="gradient-text block w-auto text-2xl font-bold leading-none sm:text-4xl lg:text-4xl">
          PxlMorph AI Studio
        </h1>
        <p className="block w-auto text-base leading-none text-slate-300 sm:text-lg lg:text-base">
          Transform any photo with the magic of AI-powered style transfer
        </p>
      </div>
    </div>
  );
}
