import React from "react";

/**
 * StudioColumns: Two-column layout for Studio page.
 * - Left: main content (upload, style, etc.)
 * - Right: navbar (recent creations)
 * Uses <main> and <aside> for semantic HTML, and <section> for logical content blocks.
 */
export function StudioColumns({ main, navbar }: { main: React.ReactNode; navbar: React.ReactNode }) {
  return (
    <div className="flex w-full flex-col items-start gap-4 lg:flex-row">
      <main className="flex w-full min-w-0 flex-col items-stretch" aria-label="Main content">
        {main}
      </main>
      {/* Hidden on mobile/medium, visible and responsive on desktop */}
      <aside 
        className="hidden w-auto min-w-[240px] max-w-[340px] shrink-0 transition-all duration-200 lg:block" 
        style={{ flex: '0 1 28%' }}
        aria-label="Recent creations navbar"
      >
        <section className="sticky top-4 w-full">{navbar}</section>
      </aside>
    </div>
  );
} 