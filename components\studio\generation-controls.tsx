
"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ImageUploader } from "@/components/studio/image-uploader";
import { MemoizedStyleSelector } from "@/components/studio/style-selector";
import {
  <PERSON><PERSON>les,
  AlertTriangle,
  Zap,
  WifiOff,
  Upload,
  Wand2,
} from "lucide-react";
import { User } from "@/lib/types";

interface GenerationControlsProps {
  user: User | null;
  isApiConfigured: boolean;
  isOnline: boolean;
  isGenerating: boolean;
  generationProgress: string;
  selectedStyle: string;
  uploadedImage: File | null; // Changed from inspirationImage
  customPrompt: string;
  hasUsedCurrentImage?: boolean; // New prop to track if image has been used
  onStyleSelect: (style: string) => void;
  onUploadedImageSelect: (file: File | null) => void; // Changed from onInspirationImageSelect
  onCustomPromptChange: (prompt: string) => void;
  onGenerate: () => void;
}

export function GenerationControls({
  user,
  isApiConfigured,
  isOnline,
  isGenerating,
  generationProgress,
  selectedStyle,
  uploadedImage, // Changed from inspirationImage
  customPrompt,
  hasUsedCurrentImage = false, // New prop to track if image has been used
  onStyleSelect,
  onUploadedImageSelect, // Changed from onInspirationImageSelect
  onCustomPromptChange,
  onGenerate,
}: GenerationControlsProps) {
  return (
    <div className="flex flex-col gap-y-4 xl:col-span-2">

      {/* API Key Warning */}
      { !isApiConfigured && (
        <section role="alert">
          <Alert className="glass-card border-orange-500/30 bg-orange-500/10">
            <AlertTriangle className="size-5 text-orange-400" />
            <AlertDescription className="text-orange-200">
              OpenAI API key not configured. Please add your OPENAI_API_KEY
              environment variable to generate images.
            </AlertDescription>
          </Alert>
        </section>
      )}

      {/* Network Status Alert */}
      { !isOnline && (
        <section role="alert">
          <Alert className="glass-card border-red-500/30 bg-red-500/10">
            <WifiOff className="size-5 text-red-400" />
            <AlertDescription className="text-red-200">
              You are currently offline. Image generation requires an internet
              connection.
            </AlertDescription>
          </Alert>
        </section>
      )}

      <section>
        <h2 className="sr-only">Upload Your Image</h2>
        <Card className="glass-card card-entrance">
          <CardHeader className="pb-2 pt-6">
            <CardTitle className="flex items-center gap-3 text-xl">
              <div className="rounded-lg bg-gradient-to-r from-violet-500 to-purple-500 p-2">
                <Upload className="size-5 text-white" />
              </div>
              Upload Your Image
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ImageUploader
              onImageSelect={onUploadedImageSelect}
              selectedImage={uploadedImage}
              hasBeenUsed={hasUsedCurrentImage}
            />
          </CardContent>
        </Card>
      </section>

      <section>
        <h2 className="sr-only">Choose Your Artistic Style and Generate</h2>
        <Card className="glass-card card-entrance">
          <CardHeader className="pb-2 pt-6">
            <CardTitle className="flex items-center gap-3 text-xl">
              <div className="rounded-lg bg-gradient-to-r from-violet-500 to-blue-500 p-2">
                <Sparkles className="size-5 text-white" />
              </div>
              Choose Your Artistic Style
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col items-center gap-6">
              <MemoizedStyleSelector
                selectedStyle={selectedStyle}
                onStyleSelect={onStyleSelect}
              />
              <Button
                onClick={onGenerate}
                size="lg"
                className={`hover-lift w-full max-w-sm rounded-2xl border-0 px-12 py-7 text-xl font-bold text-white shadow-2xl transition-all duration-300 ${
                  !uploadedImage || isGenerating || !isApiConfigured || !isOnline
                    ? 'cursor-not-allowed bg-slate-600'
                    : 'transform-btn-animated btn-glow'
                }`}
                disabled={!uploadedImage || isGenerating || !isApiConfigured || !isOnline}
                aria-label="Transform Now"
              >
                {isGenerating ? (
                  <span className="mr-3 inline-block size-6 animate-spin rounded-full border-2 border-white border-t-transparent align-middle" />
                ) : (
                  <Wand2 className="-ml-1 mr-3 size-6" />
                )}
                Transform Now
              </Button>
            </div>
          </CardContent>
        </Card>
      </section>
    </div>
  );
}
