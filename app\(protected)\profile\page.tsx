"use client";
export const dynamic = "force-dynamic";
// Route: /profile
// Profile Page (protected)
// Shows user profile and stats. Requires authentication.
import ProfileClient from './ProfileClient';
import ProtectedRoute from "@/components/auth/ProtectedRoute";

export default function ProfilePage() {
  return (
    <ProtectedRoute>
      <div className="flex w-full items-center justify-center" aria-label="Profile main content">
        <div className="flex w-full justify-center">
          <ProfileClient />
        </div>
      </div>
    </ProtectedRoute>
  );
}
