
"use client";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { useAllUserImages } from "@/lib/hooks/useAllUserImages";
import { useRecentCreations } from "@/lib/hooks/useRecentCreations";
import { formatDateLocale } from "@/lib/utils/format";
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogHeader as DialogHeaderUI,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { LogOut, User as User<PERSON><PERSON>, <PERSON><PERSON>l, KeyRound, Share2, Heart, Star, Clock, Crown, Award, Link2, Loader2, Spark<PERSON> } from "lucide-react";
import Image from "next/image";
import {
  TooltipProvider,
  Tooltip,
  TooltipTrigger,
  TooltipContent,
  TooltipArrow,
} from "@/components/ui/tooltip";
import { useStyles } from "@/lib/contexts/StylesContext";
import { useUser } from "@/lib/contexts/UserContext";

export default function ProfileClient() {
  const router = useRouter();
  const { user, isLoading: userLoading, signOut } = useUser();
  const { images, isLoading: imagesLoading } = useAllUserImages(user?.id);
  const { recentImages, isLoading: recentLoading } = useRecentCreations(user?.id);
  const [editOpen, setEditOpen] = useState(false);
  const [displayName, setDisplayName] = useState(() => {
    if (typeof window !== "undefined") {
      return localStorage.getItem("displayName") || "";
    }
    return "";
  });
  const [editLoading, setEditLoading] = useState(false);
  const { styles: allStyles } = useStyles();
  function getStyleTitle(styleId: string) {
    const found = allStyles.find((s) => s.id === styleId);
    return found ? found.name : (styleId || "").replace(/-/g, " ").replace(/\b\w/g, (c: string) => c.toUpperCase());
  }

  // Stats
  const imagesGenerated = images?.length || 0;
  const imagesShared = images?.filter((img) => img.is_shared).length || 0;
  const likesReceived = images?.reduce((sum, img) => sum + (img.like_count || 0), 0) || 0;
  // User type does not have favorites property; set to 0 or use context if available
  const favoritesCount = 0;

  // Achievements
  const achievements = [];
  if (imagesGenerated >= 100) achievements.push({ icon: Award, label: "100+ Images" });
  if (likesReceived >= 50) achievements.push({ icon: Heart, label: "50+ Likes" });
  if (imagesShared >= 25) achievements.push({ icon: Share2, label: "25+ Shares" });

  // Fallbacks
  const email = user?.email || "<EMAIL>";
  const name = displayName || email.split("@")[0] || "User";
  const joined = user?.created_at ? formatDateLocale(user.created_at) : "-";
  const avatarUrl = user && typeof user === "object" && "avatar_url" in user && (user as any).avatar_url
    ? (user as any).avatar_url
    : "https://github.com/shadcn.png";

  // Loading state
  if (userLoading || imagesLoading) {
    return (
      <div className="flex items-center justify-center">
        <Loader2 className="size-8 animate-spin text-violet-500" aria-label="Loading profile" />
      </div>
    );
  }
  if (!user) {
    return null;
  }

  // Edit profile mock submit
  const handleEditProfile = (e: React.FormEvent) => {
    e.preventDefault();
    setEditLoading(true);
    setTimeout(() => {
      setEditLoading(false);
      setEditOpen(false);
      localStorage.setItem("displayName", displayName);
      toast.success("Display name updated!");
    }, 1200);
  };

  // Change password action
  const handleChangePassword = () => {
    toast.info("Change password coming soon!");
  };

  // Sign out action
  const handleSignOut = async () => {
    try {
      await signOut(); // Actually logs the user out
      // toast.success("Signed out successfully"); // Remove this, handled by login page
      // setTimeout(() => {
      //   router.push("/login?signout=1"); // Use signout param for best practice
      // }, 500);
    } catch {
      toast.error("Sign out failed");
    }
  };

  return (
    <>
      <div className="flex w-full h-full">
        <Card className="glass-card card-entrance w-full h-full overflow-y-auto">
        <CardHeader className="flex flex-col items-center gap-3 p-4 text-center sm:gap-4 sm:p-6 md:gap-6 md:p-8 lg:gap-8 lg:p-12 xl:p-16">
          <div className="animate-float relative flex flex-col items-center">
            <Avatar className="size-24 sm:size-28 md:size-32 lg:size-36 xl:size-40 2xl:size-44 border-2 sm:border-3 md:border-4 border-violet-400/60 shadow-xl ring-2 sm:ring-3 md:ring-4 ring-violet-500/10 transition-transform duration-300 hover:scale-105 focus:scale-105">
              <AvatarImage src={avatarUrl} alt={email} />
              <AvatarFallback className="bg-violet-600 text-2xl sm:text-3xl md:text-4xl lg:text-5xl text-white">
                <UserIcon className="size-6 sm:size-8 md:size-10 lg:size-12 xl:size-14" />
              </AvatarFallback>
            </Avatar>
            <Badge
              className="badge-glass animate-gradient-move absolute -bottom-4 left-1/2 flex -translate-x-1/2 flex-row items-center gap-2 border border-violet-400/30 bg-gradient-to-r from-violet-500/80 via-blue-500/80 to-cyan-400/80 px-4 py-1.5 text-base font-semibold text-white shadow-md"
              aria-label="Pro Member"
              style={{
                boxShadow: "0 2px 12px 0 rgba(139,92,246,0.10)",
                backdropFilter: "blur(18px)",
                backgroundSize: "400% 400%",
              }}
            >
              <Crown className="mr-1 size-5 text-yellow-300 drop-shadow" />
              <span className="whitespace-nowrap font-semibold tracking-wide">Pro Member</span>
            </Badge>
            {achievements.length > 0 && (
              <div className="mt-8 flex flex-wrap justify-center gap-2">
                {achievements.map((ach, i) => (
                  <Badge key={i} className="badge-glass flex items-center gap-1 border border-pink-300/30 bg-gradient-to-r from-pink-500/80 to-yellow-400/80 px-3 py-1 text-sm font-semibold text-white shadow-md">
                    <ach.icon className="size-4" />
                    {ach.label}
                  </Badge>
                ))}
              </div>
            )}
          </div>
          <CardTitle className="gradient-text animate-gradient-text select-text pb-1 text-xl font-extrabold leading-tight drop-shadow-2xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl 2xl:text-6xl">
            {name}
          </CardTitle>
          <CardDescription className="select-text text-sm font-medium text-blue-200/90 sm:text-base md:text-lg lg:text-xl xl:text-2xl">
            {email}
          </CardDescription>
          <div className="mt-1 text-xs sm:text-sm md:text-base text-slate-400">Joined: {joined}</div>
        </CardHeader>
        <CardContent className="relative z-10 space-y-4 p-4 sm:space-y-6 sm:p-6 md:space-y-8 md:p-8 lg:space-y-10 lg:p-12 xl:p-16">
          {/* Stats Row */}
          <TooltipProvider delayDuration={100}>
            <div className="mb-2 grid grid-cols-2 gap-2 text-center sm:gap-3 md:grid-cols-4 md:gap-4 lg:gap-6 xl:gap-8">
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="cursor-pointer">
                    <div className="group flex items-center justify-center gap-1 text-sm font-bold text-blue-400 transition-transform duration-200 hover:scale-105 sm:text-base md:text-lg lg:text-xl xl:text-2xl">
                      <Sparkles className="inline-block size-4 sm:size-5 md:size-6 text-blue-400" />
                      {imagesGenerated}
                    </div>
                    <div className="text-xs sm:text-sm md:text-base text-slate-400">Generated</div>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  Total images generated
                  <TooltipArrow className="fill-slate-900/70" />
                </TooltipContent>
              </Tooltip>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="cursor-pointer">
                    <div className="group flex items-center justify-center gap-1 text-sm font-bold text-emerald-400 transition-transform duration-200 hover:scale-105 sm:text-base md:text-lg lg:text-xl xl:text-2xl">
                      <Share2 className="inline-block size-4 sm:size-5 md:size-6 text-emerald-400" />{imagesShared}
                    </div>
                    <div className="text-xs sm:text-sm md:text-base text-slate-400">Shared</div>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  Total images shared
                  <TooltipArrow className="fill-slate-900/70" />
                </TooltipContent>
              </Tooltip>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="cursor-pointer">
                    <div className="group flex items-center justify-center gap-1 text-sm font-bold text-pink-600 transition-transform duration-200 hover:scale-105 sm:text-base md:text-lg lg:text-xl xl:text-2xl">
                      <Heart className="inline-block size-4 sm:size-5 md:size-6 text-pink-600" />{likesReceived}
                    </div>
                    <div className="text-xs sm:text-sm md:text-base text-slate-400">Likes</div>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  Total likes received
                  <TooltipArrow className="fill-slate-900/70" />
                </TooltipContent>
              </Tooltip>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="cursor-pointer">
                    <div className="group flex items-center justify-center gap-1 text-sm font-bold text-yellow-400 transition-transform duration-200 hover:scale-105 sm:text-base md:text-lg lg:text-xl xl:text-2xl">
                      <Star className="inline-block size-4 sm:size-5 md:size-6 text-yellow-400" />{favoritesCount}
                    </div>
                    <div className="text-xs sm:text-sm md:text-base text-slate-400">Favorites</div>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  Total favorite styles
                  <TooltipArrow className="fill-slate-900/70" />
                </TooltipContent>
              </Tooltip>
            </div>
          </TooltipProvider>

          {/* Quick Actions */}
          <div className="mt-2 flex flex-col gap-3 sm:flex-row sm:gap-4 md:gap-6">
            <Dialog open={editOpen} onOpenChange={setEditOpen}>
              <DialogTrigger asChild>
                <Button
                  className="btn-glow glass-card flex flex-1 items-center justify-center gap-2 rounded-full border border-violet-400/30 bg-gradient-to-r from-violet-600/80 to-blue-600/80 px-4 py-2 text-sm font-semibold text-white shadow-md transition-all duration-150 hover:scale-[1.03] focus-visible:ring-2 focus-visible:ring-violet-500 sm:px-6 sm:text-base md:px-8 md:py-3 md:text-lg"
                  aria-label="Edit Profile"
                >
                  <Pencil className="size-4 sm:size-5 md:size-6 text-blue-200" />
                  <span>Edit Profile</span>
                </Button>
              </DialogTrigger>
              <DialogContent className="animate-modal-fade max-w-md border-violet-500/30 bg-slate-900/95 backdrop-blur-xl">
                <DialogHeaderUI className="space-y-2 text-center">
                  <DialogTitle>Edit Profile</DialogTitle>
                  <DialogDescription>Update your display name</DialogDescription>
                </DialogHeaderUI>
                <form onSubmit={handleEditProfile} className="space-y-4">
                  <div>
                    <Label htmlFor="displayName">Display Name</Label>
                    <Input
                      id="displayName"
                      value={displayName}
                      onChange={(e) => setDisplayName(e.target.value)}
                      placeholder="Enter display name"
                      autoFocus
                      className="mt-1"
                      aria-label="Display Name"
                      maxLength={32}
                    />
                  </div>
                  <DialogFooter>
                    <Button type="submit" disabled={editLoading} aria-label="Save Display Name" className="btn-consistent bg-blue-500/80 text-white hover:bg-blue-500/90">
                      {editLoading ? <span className="mr-2 inline-block size-4 animate-pulse rounded-full bg-slate-800" /> : null}Save
                    </Button>
                    <DialogClose asChild>
                      <Button type="button" variant="outline" aria-label="Cancel Edit">Cancel</Button>
                    </DialogClose>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
            <Button
              className="btn-glow glass-card flex flex-1 items-center justify-center gap-2 rounded-full border border-emerald-300/30 bg-gradient-to-r from-emerald-500/80 to-teal-400/80 px-6 py-2 font-semibold text-white shadow-md transition-all duration-150 hover:scale-[1.03] focus-visible:ring-2 focus-visible:ring-emerald-400"
              onClick={handleChangePassword}
              aria-label="Change Password"
            >
              <KeyRound className="size-5 text-emerald-100" />
              <span>Change Password</span>
            </Button>
            <Button
              className="btn-glow flex flex-1 items-center justify-center gap-2 rounded-full border border-red-500 bg-gradient-to-r from-red-600 to-red-700 px-6 py-2 font-semibold text-white shadow-lg transition-all duration-150 hover:scale-105 hover:shadow-[0_0_0_4px_rgba(220,38,38,0.35)] focus-visible:ring-2 focus-visible:ring-red-500"
              onClick={handleSignOut}
              aria-label="Sign Out"
            >
              <LogOut className="size-5 text-white transition-colors duration-150" />
              <span>Sign Out</span>
            </Button>
          </div>

          {/* Recent Activity */}
          <div className="mt-4">
            <div className="mb-3 flex items-center gap-2 text-base font-semibold text-slate-200 sm:text-lg">
              <Clock className="size-5 text-violet-400" /> Recent Activity
            </div>
            <ul className="space-y-1 text-xs text-slate-300 sm:space-y-2 sm:text-sm md:text-base">
              {recentLoading ? (
                <li className="flex animate-pulse items-center gap-2"><Loader2 className="size-4 animate-spin" /> Loading...</li>
              ) : recentImages && recentImages.length > 0 ? (
                recentImages.slice(0, 5).map((img) => (
                  <li key={img.id} className="animate-fadeInUp grid grid-cols-[16px_90px_70px_1fr] items-center gap-2">
                    <span className={`size-2 rounded-full ${img.is_shared ? "bg-emerald-400" : "bg-blue-400"} mx-auto`} />
                    <span className="font-semibold tabular-nums text-violet-200">{img.created_at ? formatDateLocale(img.created_at) : "-"}</span>
                    <span className={`text-center text-xs font-semibold ${img.is_shared ? "text-emerald-400" : "text-blue-400"}`}>{img.is_shared ? "Shared" : "Created"}</span>
                    <span className="truncate font-medium text-slate-200">{getStyleTitle(img.style)}</span>
                  </li>
                ))
              ) : (
                <li className="text-slate-500">No recent activity.</li>
              )}
            </ul>
          </div>
        </CardContent>
        </Card>
      </div>

    </>
  );
}
